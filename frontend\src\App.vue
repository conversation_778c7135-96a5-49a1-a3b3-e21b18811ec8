<template>
  <n-config-provider :theme="theme">
    <div class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header-content">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">干</span>
            </div>
            <h1 class="text-xl font-bold text-gray-800">干部授课管理系统</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">欢迎使用</span>
            <n-button @click="toggleTheme" quaternary circle>
              <template #icon>
                <n-icon :component="theme ? SunnyOutline : MoonOutline" />
              </template>
            </n-button>
          </div>
        </div>
      </header>

      <!-- 主体内容区域 -->
      <div class="main-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
          <n-menu
            :options="menuOptions"
            :value="$route.name"
            @update:value="handleMenuSelect"
            class="h-full"
          />
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
          <div class="content-wrapper">
            <router-view />
          </div>
        </main>
      </div>
    </div>
  </n-config-provider>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  NConfigProvider,
  NMenu,
  NButton,
  NIcon,
  darkTheme
} from 'naive-ui'
import {
  HomeOutline,
  PeopleOutline,
  BusinessOutline,
  SchoolOutline,
  CalendarOutline,
  SunnyOutline,
  MoonOutline
} from '@vicons/ionicons5'

const router = useRouter()
const theme = ref(null)

const toggleTheme = () => {
  theme.value = theme.value ? null : darkTheme
}

const menuOptions = [
  {
    label: '数据总览',
    key: 'Dashboard',
    icon: () => h(NIcon, { component: HomeOutline })
  },
  {
    label: '干部管理',
    key: 'Cadres',
    icon: () => h(NIcon, { component: PeopleOutline })
  },
  {
    label: '单位管理',
    key: 'Units',
    icon: () => h(NIcon, { component: BusinessOutline })
  },
  {
    label: '高校管理',
    key: 'Universities',
    icon: () => h(NIcon, { component: SchoolOutline })
  },
  {
    label: '授课计划',
    key: 'Plans',
    icon: () => h(NIcon, { component: CalendarOutline })
  }
]

const handleMenuSelect = (key) => {
  router.push({ name: key })
}
</script>
