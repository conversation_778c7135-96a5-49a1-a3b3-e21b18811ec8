<template>
  <n-config-provider :theme="theme">
    <n-layout class="min-h-screen">
      <n-layout-header class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center space-x-4">
          <h1 class="text-xl font-bold text-gray-800">干部授课管理系统</h1>
        </div>
        <div class="ml-auto">
          <n-button @click="toggleTheme" quaternary>
            <template #icon>
              <n-icon :component="theme ? SunnyOutline : MoonOutline" />
            </template>
          </n-button>
        </div>
      </n-layout-header>

      <n-layout has-sider class="flex-1">
        <n-layout-sider
          bordered
          collapse-mode="width"
          :collapsed-width="64"
          :width="240"
          show-trigger
        >
          <n-menu
            :collapsed-width="64"
            :collapsed-icon-size="22"
            :options="menuOptions"
            :value="$route.name"
            @update:value="handleMenuSelect"
          />
        </n-layout-sider>

        <n-layout-content class="p-6">
          <router-view />
        </n-layout-content>
      </n-layout>
    </n-layout>
  </n-config-provider>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  NConfigProvider,
  NLayout,
  NLayoutHeader,
  NLayoutSider,
  NLayoutContent,
  NMenu,
  NButton,
  NIcon,
  darkTheme
} from 'naive-ui'
import {
  HomeOutline,
  PeopleOutline,
  BusinessOutline,
  SchoolOutline,
  CalendarOutline,
  SunnyOutline,
  MoonOutline
} from '@vicons/ionicons5'

const router = useRouter()
const theme = ref(null)

const toggleTheme = () => {
  theme.value = theme.value ? null : darkTheme
}

const menuOptions = [
  {
    label: '数据总览',
    key: 'Dashboard',
    icon: () => h(NIcon, { component: HomeOutline })
  },
  {
    label: '干部管理',
    key: 'Cadres',
    icon: () => h(NIcon, { component: PeopleOutline })
  },
  {
    label: '单位管理',
    key: 'Units',
    icon: () => h(NIcon, { component: BusinessOutline })
  },
  {
    label: '高校管理',
    key: 'Universities',
    icon: () => h(NIcon, { component: SchoolOutline })
  },
  {
    label: '授课计划',
    key: 'Plans',
    icon: () => h(NIcon, { component: CalendarOutline })
  }
]

const handleMenuSelect = (key) => {
  router.push({ name: key })
}
</script>
