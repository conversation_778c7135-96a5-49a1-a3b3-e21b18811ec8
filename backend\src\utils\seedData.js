const mongoose = require('mongoose');
const Unit = require('../models/Unit');
const University = require('../models/University');
const Cadre = require('../models/Cadre');
const Plan = require('../models/Plan');

// 模拟单位数据
const sampleUnits = [
  {
    name: '中共中央组织部',
    shortName: '中组部',
    calibrationCode: 'ZZB001',
    receiptCode: 'R001',
    unitType: '国家机关',
    unitLevel: '正部级',
    contactPerson: '张三',
    landlinePhone: '010-12345678',
    mobilePhone: '13800138001',
    remarks: '负责全国组织工作'
  },
  {
    name: '教育部',
    shortName: '教育部',
    calibrationCode: 'JYB001',
    receiptCode: 'R002',
    unitType: '国家机关',
    unitLevel: '正部级',
    contactPerson: '李四',
    landlinePhone: '010-12345679',
    mobilePhone: '13800138002',
    remarks: '负责全国教育工作'
  },
  {
    name: '国家发展和改革委员会',
    shortName: '发改委',
    calibrationCode: 'FGW001',
    receiptCode: 'R003',
    unitType: '国家机关',
    unitLevel: '正部级',
    contactPerson: '王五',
    landlinePhone: '010-12345680',
    mobilePhone: '13800138003',
    remarks: '负责国家发展改革工作'
  }
];

// 模拟高校数据
const sampleUniversities = [
  {
    schoolName: '清华大学',
    schoolCode: 'THU001',
    governingDepartment: '教育部',
    location: '北京',
    isCentral: true,
    contactDepartment: '继续教育学院',
    contactPosition: '院长',
    contactName: '赵六',
    landlinePhone: '010-62785001',
    mobilePhone: '13800138004',
    remarks: '国内顶尖高校'
  },
  {
    schoolName: '北京大学',
    schoolCode: 'PKU001',
    governingDepartment: '教育部',
    location: '北京',
    isCentral: true,
    contactDepartment: '继续教育部',
    contactPosition: '主任',
    contactName: '孙七',
    landlinePhone: '010-62751001',
    mobilePhone: '13800138005',
    remarks: '综合性研究型大学'
  },
  {
    schoolName: '复旦大学',
    schoolCode: 'FDU001',
    governingDepartment: '教育部',
    location: '上海',
    isCentral: true,
    contactDepartment: '继续教育学院',
    contactPosition: '副院长',
    contactName: '周八',
    landlinePhone: '021-65642001',
    mobilePhone: '13800138006',
    remarks: '知名综合性大学'
  }
];

const seedData = async (force = false) => {
  try {
    console.log('开始初始化数据...');

    // 检查是否已有数据
    const [unitCount, universityCount, cadreCount, planCount] = await Promise.all([
      Unit.countDocuments(),
      University.countDocuments(),
      Cadre.countDocuments(),
      Plan.countDocuments()
    ]);

    const hasData = unitCount > 0 || universityCount > 0 || cadreCount > 0 || planCount > 0;

    if (hasData && !force) {
      console.log('数据库中已有数据:');
      console.log(`- 单位: ${unitCount} 个`);
      console.log(`- 高校: ${universityCount} 个`);
      console.log(`- 干部: ${cadreCount} 个`);
      console.log(`- 授课计划: ${planCount} 个`);
      console.log('如需重新初始化，请使用 force 参数');
      return;
    }

    if (force) {
      console.log('强制重新初始化，清空现有数据...');
      // 清空现有数据
      await Promise.all([
        Plan.deleteMany({}),
        Cadre.deleteMany({}),
        University.deleteMany({}),
        Unit.deleteMany({})
      ]);
    }
    
    // 插入单位数据
    const units = await Unit.insertMany(sampleUnits);
    console.log(`插入 ${units.length} 个单位`);
    
    // 插入高校数据
    const universities = await University.insertMany(sampleUniversities);
    console.log(`插入 ${universities.length} 个高校`);
    
    // 插入干部数据
    const sampleCadres = [
      {
        name: '李明',
        gender: '男',
        birthDate: new Date('1975-03-15'),
        nativePlace: '北京市',
        level: '厅局级',
        position: '司长',
        cadreType: '厅局级干部',
        unitId: units[0]._id,
        highestEducation: '博士研究生',
        highestDegree: '博士',
        expertise: '组织管理、人才培养',
        proposedTeachingDirections: ['坚持党的全面领导', '全面从严治党'],
        intendedUniversity1: universities[0]._id,
        intendedUniversity2: universities[1]._id,
        remarks: '经验丰富的组织工作专家'
      },
      {
        name: '王芳',
        gender: '女',
        birthDate: new Date('1980-07-22'),
        nativePlace: '上海市',
        level: '厅局级',
        position: '副司长',
        cadreType: '厅局级干部',
        unitId: units[1]._id,
        highestEducation: '硕士研究生',
        highestDegree: '硕士',
        expertise: '教育政策、课程改革',
        proposedTeachingDirections: ['社会主义现代化建设的教育、科技、人才战略'],
        intendedUniversity1: universities[1]._id,
        intendedUniversity2: universities[2]._id,
        remarks: '教育领域专家'
      },
      {
        name: '张伟',
        gender: '男',
        birthDate: new Date('1978-11-08'),
        nativePlace: '广东省',
        level: '县处级',
        position: '处长',
        cadreType: '厅局级干部',
        unitId: units[2]._id,
        highestEducation: '博士研究生',
        highestDegree: '博士',
        expertise: '经济发展、产业政策',
        proposedTeachingDirections: ['推动高质量发展', '全面深化改革开放'],
        intendedUniversity1: universities[0]._id,
        intendedUniversity3: universities[2]._id,
        remarks: '经济发展专家'
      }
    ];
    
    const cadres = await Cadre.insertMany(sampleCadres);
    console.log(`插入 ${cadres.length} 个干部`);
    
    // 插入授课计划数据
    const samplePlans = [
      {
        cadreId: cadres[0]._id,
        universityId: universities[0]._id,
        courseSeries: '党建与廉政',
        courseName: '新时代党的建设总要求',
        classDate: new Date('2025-06-15'),
        classTime: '09:00-11:00',
        location: '清华大学主楼报告厅',
        plannedHours: 2,
        teachingStatus: '计划中',
        teachingSemester: '2024-2025学年第二学期',
        remarks: '重点讲解党建理论'
      },
      {
        cadreId: cadres[1]._id,
        universityId: universities[1]._id,
        courseSeries: '社会建设与民生',
        courseName: '教育现代化2035规划解读',
        classDate: new Date('2025-06-18'),
        classTime: '14:00-16:00',
        location: '北京大学英杰交流中心',
        plannedHours: 2,
        teachingStatus: '计划中',
        teachingSemester: '2024-2025学年第二学期',
        remarks: '教育政策解读'
      },
      {
        cadreId: cadres[2]._id,
        universityId: universities[2]._id,
        courseSeries: '经济发展与改革',
        courseName: '高质量发展的理论与实践',
        classDate: new Date('2025-06-20'),
        classTime: '10:00-12:00',
        location: '复旦大学光华楼',
        plannedHours: 2,
        teachingStatus: '计划中',
        teachingSemester: '2024-2025学年第二学期',
        remarks: '经济发展专题讲座'
      }
    ];
    
    const plans = await Plan.insertMany(samplePlans);
    console.log(`插入 ${plans.length} 个授课计划`);
    
    console.log('数据初始化完成！');
    
  } catch (error) {
    console.error('数据初始化失败:', error);
  }
};

module.exports = seedData;
