const Cadre = require('../models/Cadre');
const Unit = require('../models/Unit');
const University = require('../models/University');

// 获取所有干部
const getAllCadres = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, unitId, level, cadreType } = req.query;
    
    // 构建查询条件
    const query = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { position: { $regex: search, $options: 'i' } },
        { expertise: { $regex: search, $options: 'i' } }
      ];
    }
    if (unitId) query.unitId = unitId;
    if (level) query.level = level;
    if (cadreType) query.cadreType = cadreType;

    const cadres = await Cadre.find(query)
      .populate('unitId', 'name shortName')
      .populate('intendedUniversity1', 'schoolName')
      .populate('intendedUniversity2', 'schoolName')
      .populate('intendedUniversity3', 'schoolName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Cadre.countDocuments(query);

    res.json({
      cadres,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: '获取干部列表失败', message: error.message });
  }
};

// 根据ID获取干部
const getCadreById = async (req, res) => {
  try {
    const cadre = await Cadre.findById(req.params.id)
      .populate('unitId')
      .populate('intendedUniversity1')
      .populate('intendedUniversity2')
      .populate('intendedUniversity3');
    
    if (!cadre) {
      return res.status(404).json({ error: '干部不存在' });
    }
    
    res.json(cadre);
  } catch (error) {
    res.status(500).json({ error: '获取干部信息失败', message: error.message });
  }
};

// 创建干部
const createCadre = async (req, res) => {
  try {
    // 验证单位是否存在
    const unit = await Unit.findById(req.body.unitId);
    if (!unit) {
      return res.status(400).json({ error: '所属单位不存在' });
    }

    // 验证意向高校是否存在
    const universities = [];
    if (req.body.intendedUniversity1) {
      const uni1 = await University.findById(req.body.intendedUniversity1);
      if (!uni1) return res.status(400).json({ error: '意向高校1不存在' });
      universities.push(uni1);
    }
    if (req.body.intendedUniversity2) {
      const uni2 = await University.findById(req.body.intendedUniversity2);
      if (!uni2) return res.status(400).json({ error: '意向高校2不存在' });
      universities.push(uni2);
    }
    if (req.body.intendedUniversity3) {
      const uni3 = await University.findById(req.body.intendedUniversity3);
      if (!uni3) return res.status(400).json({ error: '意向高校3不存在' });
      universities.push(uni3);
    }

    const cadre = new Cadre(req.body);
    await cadre.save();
    
    // 返回包含关联数据的干部信息
    const populatedCadre = await Cadre.findById(cadre._id)
      .populate('unitId')
      .populate('intendedUniversity1')
      .populate('intendedUniversity2')
      .populate('intendedUniversity3');
    
    res.status(201).json(populatedCadre);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: '数据验证失败', message: error.message });
    }
    res.status(500).json({ error: '创建干部失败', message: error.message });
  }
};

// 更新干部
const updateCadre = async (req, res) => {
  try {
    // 验证单位是否存在
    if (req.body.unitId) {
      const unit = await Unit.findById(req.body.unitId);
      if (!unit) {
        return res.status(400).json({ error: '所属单位不存在' });
      }
    }

    // 验证意向高校是否存在
    if (req.body.intendedUniversity1) {
      const uni1 = await University.findById(req.body.intendedUniversity1);
      if (!uni1) return res.status(400).json({ error: '意向高校1不存在' });
    }
    if (req.body.intendedUniversity2) {
      const uni2 = await University.findById(req.body.intendedUniversity2);
      if (!uni2) return res.status(400).json({ error: '意向高校2不存在' });
    }
    if (req.body.intendedUniversity3) {
      const uni3 = await University.findById(req.body.intendedUniversity3);
      if (!uni3) return res.status(400).json({ error: '意向高校3不存在' });
    }

    const cadre = await Cadre.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('unitId')
      .populate('intendedUniversity1')
      .populate('intendedUniversity2')
      .populate('intendedUniversity3');
    
    if (!cadre) {
      return res.status(404).json({ error: '干部不存在' });
    }
    
    res.json(cadre);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: '数据验证失败', message: error.message });
    }
    res.status(500).json({ error: '更新干部失败', message: error.message });
  }
};

// 删除干部
const deleteCadre = async (req, res) => {
  try {
    const cadre = await Cadre.findByIdAndDelete(req.params.id);
    
    if (!cadre) {
      return res.status(404).json({ error: '干部不存在' });
    }
    
    res.json({ message: '干部删除成功' });
  } catch (error) {
    res.status(500).json({ error: '删除干部失败', message: error.message });
  }
};

// 获取拟授课方向选项
const getTeachingDirections = (req, res) => {
  const directions = [
    '导论',
    '新时代坚持和发展中国特色社会主义',
    '以中国式现代化全面推进中华民族伟大复兴',
    '坚持党的全面领导',
    '坚持以人民为中心',
    '全面深化改革开放',
    '推动高质量发展',
    '社会主义现代化建设的教育、科技、人才战略',
    '发展全过程人民民主',
    '全面依法治国',
    '建设社会主义文化强国',
    '以保障和改善民生为重点加强社会建设',
    '建设社会主义生态文明',
    '维护和塑造国家安全',
    '建设巩固国防和强大人民军队',
    '坚持"一国两制"和推进祖国完全统一',
    '中国特色大国外交和推动构建人类命运共同体',
    '全面从严治党'
  ];
  
  res.json(directions);
};

module.exports = {
  getAllCadres,
  getCadreById,
  createCadre,
  updateCadre,
  deleteCadre,
  getTeachingDirections
};
