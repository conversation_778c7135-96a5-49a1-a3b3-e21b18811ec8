const express = require('express');
const router = express.Router();
const {
  getAllPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getThisWeekPlans
} = require('../controllers/planController');

// GET /api/plans - 获取所有授课计划
router.get('/', getAllPlans);

// GET /api/plans/this-week - 获取本周授课计划
router.get('/this-week', getThisWeekPlans);

// GET /api/plans/:id - 根据ID获取授课计划
router.get('/:id', getPlanById);

// POST /api/plans - 创建授课计划
router.post('/', createPlan);

// PUT /api/plans/:id - 更新授课计划
router.put('/:id', updatePlan);

// DELETE /api/plans/:id - 删除授课计划
router.delete('/:id', deletePlan);

module.exports = router;
