<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">数据总览</h1>
        <p class="text-gray-600 mt-1">干部授课管理系统统计数据</p>
      </div>
      <button @click="refreshData" :disabled="loading" class="btn btn-primary">
        <span v-if="loading">刷新中...</span>
        <span v-else">刷新数据</span>
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="card text-center">
        <div class="flex items-center justify-center mb-3">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <n-icon :component="CalendarOutline" size="24" class="text-blue-600" />
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900">{{ stats.totalPlans }}</h3>
        <p class="text-gray-600">授课计划总数</p>
      </div>

      <div class="card text-center">
        <div class="flex items-center justify-center mb-3">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <n-icon :component="PeopleOutline" size="24" class="text-green-600" />
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900">{{ stats.totalCadres }}</h3>
        <p class="text-gray-600">干部总数</p>
      </div>

      <div class="card text-center">
        <div class="flex items-center justify-center mb-3">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <n-icon :component="BusinessOutline" size="24" class="text-purple-600" />
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900">{{ stats.totalUnits }}</h3>
        <p class="text-gray-600">中央单位数</p>
      </div>

      <div class="card text-center">
        <div class="flex items-center justify-center mb-3">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
            <n-icon :component="SchoolOutline" size="24" class="text-orange-600" />
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900">{{ stats.totalUniversities }}</h3>
        <p class="text-gray-600">授课高校数</p>
      </div>
    </div>

    <!-- 本周授课信息 -->
    <div class="card mb-8">
      <h2 class="text-xl font-bold text-gray-900 mb-4">本周授课安排</h2>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4 font-semibold text-gray-700">日期</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">时间</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">课程名称</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">授课干部</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">授课高校</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="plan in weeklyPlans" :key="plan._id" class="border-b border-gray-100 hover:bg-gray-50">
              <td class="py-3 px-4">{{ formatDate(plan.classDate) }}</td>
              <td class="py-3 px-4">{{ plan.classTime }}</td>
              <td class="py-3 px-4">{{ plan.courseName }}</td>
              <td class="py-3 px-4">{{ plan.cadreName }}</td>
              <td class="py-3 px-4">{{ plan.universityName }}</td>
              <td class="py-3 px-4">
                <span :class="getStatusClass(plan.teachingStatus)" class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ plan.teachingStatus }}
                </span>
              </td>
            </tr>
            <tr v-if="weeklyPlans.length === 0">
              <td colspan="6" class="py-8 text-center text-gray-500">本周暂无授课安排</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <h2 class="text-xl font-bold text-gray-900 mb-4">授课状态分布</h2>
        <div ref="statusChartRef" style="height: 300px;"></div>
      </div>

      <div class="card">
        <h2 class="text-xl font-bold text-gray-900 mb-4">月度授课量对比</h2>
        <div ref="monthlyChartRef" style="height: 300px;"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { NCard, NStatistic, NIcon, NButton, NDataTable } from 'naive-ui'
import {
  CalendarOutline,
  PeopleOutline,
  BusinessOutline,
  SchoolOutline,
  RefreshOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import axios from 'axios'

const loading = ref(false)
const stats = ref({
  totalPlans: 0,
  totalCadres: 0,
  totalUnits: 0,
  totalUniversities: 0
})
const weeklyPlans = ref([])
const statusChartRef = ref(null)
const monthlyChartRef = ref(null)

const weeklyColumns = [
  { title: '日期', key: 'classDate', render: (row) => new Date(row.classDate).toLocaleDateString() },
  { title: '时间', key: 'classTime' },
  { title: '课程名称', key: 'courseName' },
  { title: '授课干部', key: 'cadreName' },
  { title: '授课高校', key: 'universityName' },
  { title: '状态', key: 'teachingStatus' }
]

const fetchData = async () => {
  loading.value = true
  try {
    // 尝试从API获取数据
    const [overviewRes, weeklyRes] = await Promise.all([
      axios.get('http://localhost:3000/api/dashboard/overview'),
      axios.get('http://localhost:3000/api/plans/this-week')
    ])

    stats.value = overviewRes.data
    weeklyPlans.value = weeklyRes.data.map(plan => ({
      ...plan,
      cadreName: plan.cadreId?.name || '未知',
      universityName: plan.universityId?.schoolName || '未知'
    }))

    await nextTick()
    initCharts()
  } catch (error) {
    console.error('获取数据失败，使用模拟数据:', error)
    // 使用模拟数据
    stats.value = {
      totalPlans: 15,
      totalCadres: 8,
      totalUnits: 5,
      totalUniversities: 12,
      plannedPlans: 8,
      ongoingPlans: 4,
      completedPlans: 3
    }

    weeklyPlans.value = [
      {
        classDate: new Date('2025-06-15'),
        classTime: '09:00-11:00',
        courseName: '新时代党的建设总要求',
        cadreName: '李明',
        universityName: '清华大学',
        teachingStatus: '计划中'
      },
      {
        classDate: new Date('2025-06-18'),
        classTime: '14:00-16:00',
        courseName: '教育现代化2035规划解读',
        cadreName: '王芳',
        universityName: '北京大学',
        teachingStatus: '计划中'
      }
    ]

    await nextTick()
    initCharts()
  } finally {
    loading.value = false
  }
}

const initCharts = () => {
  // 状态分布图
  if (statusChartRef.value) {
    const statusChart = echarts.init(statusChartRef.value)
    statusChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: stats.value.plannedPlans, name: '计划中' },
          { value: stats.value.ongoingPlans, name: '进行中' },
          { value: stats.value.completedPlans, name: '已完成' }
        ]
      }]
    })
  }
  
  // 月度对比图
  if (monthlyChartRef.value) {
    const monthlyChart = echarts.init(monthlyChartRef.value)
    monthlyChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: { type: 'value' },
      series: [
        { name: '计划', type: 'bar', data: [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65] },
        { name: '完成', type: 'bar', data: [8, 12, 18, 22, 28, 32, 38, 42, 48, 52, 58, 62] }
      ]
    })
  }
}

const refreshData = () => {
  fetchData()
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusClass = (status) => {
  const statusClasses = {
    '计划中': 'bg-blue-100 text-blue-800',
    '进行中': 'bg-yellow-100 text-yellow-800',
    '已完成': 'bg-green-100 text-green-800',
    '已取消': 'bg-red-100 text-red-800',
    '已调整': 'bg-purple-100 text-purple-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

onMounted(() => {
  fetchData()
})
</script>
