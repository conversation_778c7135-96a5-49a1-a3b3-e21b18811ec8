<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">数据总览</h1>
      <n-button @click="refreshData" :loading="loading">
        <template #icon>
          <n-icon :component="RefreshOutline" />
        </template>
        刷新数据
      </n-button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <n-card class="text-center">
        <n-statistic label="授课计划总数" :value="stats.totalPlans">
          <template #prefix>
            <n-icon :component="CalendarOutline" class="text-blue-500" />
          </template>
        </n-statistic>
      </n-card>
      
      <n-card class="text-center">
        <n-statistic label="干部总数" :value="stats.totalCadres">
          <template #prefix>
            <n-icon :component="PeopleOutline" class="text-green-500" />
          </template>
        </n-statistic>
      </n-card>
      
      <n-card class="text-center">
        <n-statistic label="中央单位数" :value="stats.totalUnits">
          <template #prefix>
            <n-icon :component="BusinessOutline" class="text-purple-500" />
          </template>
        </n-statistic>
      </n-card>
      
      <n-card class="text-center">
        <n-statistic label="授课高校数" :value="stats.totalUniversities">
          <template #prefix>
            <n-icon :component="SchoolOutline" class="text-orange-500" />
          </template>
        </n-statistic>
      </n-card>
    </div>

    <!-- 本周授课信息 -->
    <n-card title="本周授课安排">
      <n-data-table
        :columns="weeklyColumns"
        :data="weeklyPlans"
        :loading="loading"
        :pagination="false"
        size="small"
      />
    </n-card>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <n-card title="授课状态分布">
        <div ref="statusChartRef" style="height: 300px;"></div>
      </n-card>
      
      <n-card title="月度授课量对比">
        <div ref="monthlyChartRef" style="height: 300px;"></div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { NCard, NStatistic, NIcon, NButton, NDataTable } from 'naive-ui'
import {
  CalendarOutline,
  PeopleOutline,
  BusinessOutline,
  SchoolOutline,
  RefreshOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import axios from 'axios'

const loading = ref(false)
const stats = ref({
  totalPlans: 0,
  totalCadres: 0,
  totalUnits: 0,
  totalUniversities: 0
})
const weeklyPlans = ref([])
const statusChartRef = ref(null)
const monthlyChartRef = ref(null)

const weeklyColumns = [
  { title: '日期', key: 'classDate', render: (row) => new Date(row.classDate).toLocaleDateString() },
  { title: '时间', key: 'classTime' },
  { title: '课程名称', key: 'courseName' },
  { title: '授课干部', key: 'cadreName' },
  { title: '授课高校', key: 'universityName' },
  { title: '状态', key: 'teachingStatus' }
]

const fetchData = async () => {
  loading.value = true
  try {
    const [overviewRes, weeklyRes] = await Promise.all([
      axios.get('http://localhost:3000/api/dashboard/overview'),
      axios.get('http://localhost:3000/api/plans/this-week')
    ])
    
    stats.value = overviewRes.data
    weeklyPlans.value = weeklyRes.data.map(plan => ({
      ...plan,
      cadreName: plan.cadreId?.name || '未知',
      universityName: plan.universityId?.schoolName || '未知'
    }))
    
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

const initCharts = () => {
  // 状态分布图
  if (statusChartRef.value) {
    const statusChart = echarts.init(statusChartRef.value)
    statusChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: stats.value.plannedPlans, name: '计划中' },
          { value: stats.value.ongoingPlans, name: '进行中' },
          { value: stats.value.completedPlans, name: '已完成' }
        ]
      }]
    })
  }
  
  // 月度对比图
  if (monthlyChartRef.value) {
    const monthlyChart = echarts.init(monthlyChartRef.value)
    monthlyChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: { type: 'value' },
      series: [
        { name: '计划', type: 'bar', data: [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65] },
        { name: '完成', type: 'bar', data: [8, 12, 18, 22, 28, 32, 38, 42, 48, 52, 58, 62] }
      ]
    })
  }
}

const refreshData = () => {
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>
