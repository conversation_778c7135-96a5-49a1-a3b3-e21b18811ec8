<template>
  <div class="w-full h-full overflow-y-auto">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">干部管理</h1>
        <p class="text-gray-600 mt-1">管理中央单位干部信息</p>
      </div>
      <button @click="showAddModal = true" class="btn btn-primary">
        + 新增干部
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card mb-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">搜索筛选</h2>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索关键词</label>
          <input
            v-model="searchQuery"
            placeholder="搜索干部姓名、职务..."
            class="input"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">干部级别</label>
          <select v-model="levelFilter" class="input">
            <option value="">全部级别</option>
            <option v-for="option in levelOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
          <select v-model="unitFilter" class="input">
            <option value="">全部单位</option>
            <option v-for="option in unitOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="flex items-end">
          <button @click="handleSearch" class="btn btn-primary w-full">搜索</button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">干部列表</h2>
        <span class="text-sm text-gray-600">共 {{ pagination.itemCount }} 条记录</span>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4 font-semibold text-gray-700">姓名</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">性别</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">级别</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">职务</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">所属单位</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">年龄</th>
              <th class="text-left py-3 px-4 font-semibold text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="cadre in cadres" :key="cadre._id" class="border-b border-gray-100 hover:bg-gray-50">
              <td class="py-3 px-4 font-medium">{{ cadre.name }}</td>
              <td class="py-3 px-4">{{ cadre.gender }}</td>
              <td class="py-3 px-4">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  {{ cadre.level || '未设置' }}
                </span>
              </td>
              <td class="py-3 px-4">{{ cadre.position || '-' }}</td>
              <td class="py-3 px-4">{{ cadre.unitId?.name || '未知' }}</td>
              <td class="py-3 px-4">{{ cadre.age || '-' }}</td>
              <td class="py-3 px-4">
                <div class="flex space-x-2">
                  <button @click="handleEdit(cadre)" class="btn text-sm">编辑</button>
                  <button @click="handleDelete(cadre)" class="btn text-sm text-red-600 hover:bg-red-50">删除</button>
                </div>
              </td>
            </tr>
            <tr v-if="cadres.length === 0 && !loading">
              <td colspan="7" class="py-8 text-center text-gray-500">暂无数据</td>
            </tr>
            <tr v-if="loading">
              <td colspan="7" class="py-8 text-center text-gray-500">加载中...</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
        <div class="text-sm text-gray-600">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} -
          {{ Math.min(pagination.page * pagination.pageSize, pagination.itemCount) }} 条，
          共 {{ pagination.itemCount }} 条
        </div>
        <div class="flex space-x-2">
          <button
            @click="handlePageChange(pagination.page - 1)"
            :disabled="pagination.page <= 1"
            class="btn"
            :class="{ 'opacity-50 cursor-not-allowed': pagination.page <= 1 }"
          >
            上一页
          </button>
          <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">
            {{ pagination.page }} / {{ Math.ceil(pagination.itemCount / pagination.pageSize) }}
          </span>
          <button
            @click="handlePageChange(pagination.page + 1)"
            :disabled="pagination.page >= Math.ceil(pagination.itemCount / pagination.pageSize)"
            class="btn"
            :class="{ 'opacity-50 cursor-not-allowed': pagination.page >= Math.ceil(pagination.itemCount / pagination.pageSize) }"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="新增干部">
      <div class="space-y-4">
        <n-form ref="formRef" :model="formData" :rules="rules">
          <n-form-item label="姓名" path="name">
            <n-input v-model:value="formData.name" placeholder="请输入姓名" />
          </n-form-item>
          <n-form-item label="性别" path="gender">
            <n-select v-model:value="formData.gender" :options="genderOptions" placeholder="请选择性别" />
          </n-form-item>
          <n-form-item label="出生年月" path="birthDate">
            <n-date-picker v-model:value="formData.birthDate" type="date" placeholder="请选择出生年月" />
          </n-form-item>
          <n-form-item label="所属单位" path="unitId">
            <n-select v-model:value="formData.unitId" :options="unitOptions" placeholder="请选择所属单位" />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  NModal, NForm, NFormItem, NDatePicker, NSpace
} from 'naive-ui'
import axios from 'axios'

const loading = ref(false)
const cadres = ref([])
const showAddModal = ref(false)
const searchQuery = ref('')
const levelFilter = ref(null)
const unitFilter = ref(null)
const unitOptions = ref([])

const formData = ref({
  name: '',
  gender: '',
  birthDate: null,
  unitId: null
})

const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const genderOptions = [
  { label: '男', value: '男' },
  { label: '女', value: '女' },
  { label: '其他', value: '其他' }
]

const levelOptions = [
  { label: '省级', value: '省级' },
  { label: '厅局级', value: '厅局级' },
  { label: '县处级', value: '县处级' },
  { label: '乡科级', value: '乡科级' },
  { label: '其他', value: '其他' }
]



const rules = {
  name: { required: true, message: '请输入姓名', trigger: 'blur' },
  gender: { required: true, message: '请选择性别', trigger: 'change' },
  birthDate: { required: true, message: '请选择出生年月', trigger: 'change' },
  unitId: { required: true, message: '请选择所属单位', trigger: 'change' }
}

const fetchCadres = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.pageSize,
      search: searchQuery.value,
      level: levelFilter.value,
      unitId: unitFilter.value
    }
    
    const response = await axios.get('http://localhost:3000/api/cadres', { params })
    cadres.value = response.data.cadres
    pagination.value.itemCount = response.data.total
  } catch (error) {
    console.error('获取干部列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchUnits = async () => {
  try {
    const response = await axios.get('http://localhost:3000/api/units/options')
    unitOptions.value = response.data.map(unit => ({
      label: unit.name,
      value: unit._id
    }))
  } catch (error) {
    console.error('获取单位列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  fetchCadres()
}

const handlePageChange = (page) => {
  if (page >= 1 && page <= Math.ceil(pagination.value.itemCount / pagination.value.pageSize)) {
    pagination.value.page = page
    fetchCadres()
  }
}

const handleEdit = (row) => {
  // TODO: 实现编辑功能
  console.log('编辑干部:', row)
}

const handleDelete = (row) => {
  // TODO: 实现删除功能
  console.log('删除干部:', row)
}

const handleSubmit = () => {
  // TODO: 实现提交功能
  console.log('提交表单:', formData.value)
  showAddModal.value = false
}

onMounted(() => {
  fetchCadres()
  fetchUnits()
})
</script>
