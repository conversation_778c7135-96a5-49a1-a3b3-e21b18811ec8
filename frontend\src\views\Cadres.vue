<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">干部管理</h1>
      <n-button type="primary" @click="showAddModal = true">
        <template #icon>
          <n-icon :component="AddOutline" />
        </template>
        新增干部
      </n-button>
    </div>

    <!-- 搜索和筛选 -->
    <n-card>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input v-model:value="searchQuery" placeholder="搜索干部姓名、职务..." clearable />
        <n-select v-model:value="levelFilter" placeholder="选择级别" clearable :options="levelOptions" />
        <n-select v-model:value="unitFilter" placeholder="选择单位" clearable :options="unitOptions" />
        <n-button @click="handleSearch" type="primary">搜索</n-button>
      </div>
    </n-card>

    <!-- 数据表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="cadres"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
      />
    </n-card>

    <!-- 新增/编辑模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="新增干部">
      <div class="space-y-4">
        <n-form ref="formRef" :model="formData" :rules="rules">
          <n-form-item label="姓名" path="name">
            <n-input v-model:value="formData.name" placeholder="请输入姓名" />
          </n-form-item>
          <n-form-item label="性别" path="gender">
            <n-select v-model:value="formData.gender" :options="genderOptions" placeholder="请选择性别" />
          </n-form-item>
          <n-form-item label="出生年月" path="birthDate">
            <n-date-picker v-model:value="formData.birthDate" type="date" placeholder="请选择出生年月" />
          </n-form-item>
          <n-form-item label="所属单位" path="unitId">
            <n-select v-model:value="formData.unitId" :options="unitOptions" placeholder="请选择所属单位" />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h } from 'vue'
import { 
  NCard, NButton, NIcon, NInput, NSelect, NDataTable, 
  NModal, NForm, NFormItem, NDatePicker, NSpace
} from 'naive-ui'
import { AddOutline, EditOutline, TrashOutline } from '@vicons/ionicons5'
import axios from 'axios'

const loading = ref(false)
const cadres = ref([])
const showAddModal = ref(false)
const searchQuery = ref('')
const levelFilter = ref(null)
const unitFilter = ref(null)
const unitOptions = ref([])

const formData = ref({
  name: '',
  gender: '',
  birthDate: null,
  unitId: null
})

const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const genderOptions = [
  { label: '男', value: '男' },
  { label: '女', value: '女' },
  { label: '其他', value: '其他' }
]

const levelOptions = [
  { label: '省级', value: '省级' },
  { label: '厅局级', value: '厅局级' },
  { label: '县处级', value: '县处级' },
  { label: '乡科级', value: '乡科级' },
  { label: '其他', value: '其他' }
]

const columns = [
  { title: '姓名', key: 'name' },
  { title: '性别', key: 'gender' },
  { title: '级别', key: 'level' },
  { title: '职务', key: 'position' },
  { title: '所属单位', key: 'unitName', render: (row) => row.unitId?.name || '未知' },
  { title: '年龄', key: 'age' },
  {
    title: '操作',
    key: 'actions',
    render: (row) => h('div', { class: 'space-x-2' }, [
      h(NButton, { size: 'small', onClick: () => handleEdit(row) }, { default: () => '编辑' }),
      h(NButton, { size: 'small', type: 'error', onClick: () => handleDelete(row) }, { default: () => '删除' })
    ])
  }
]

const rules = {
  name: { required: true, message: '请输入姓名', trigger: 'blur' },
  gender: { required: true, message: '请选择性别', trigger: 'change' },
  birthDate: { required: true, message: '请选择出生年月', trigger: 'change' },
  unitId: { required: true, message: '请选择所属单位', trigger: 'change' }
}

const fetchCadres = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.pageSize,
      search: searchQuery.value,
      level: levelFilter.value,
      unitId: unitFilter.value
    }
    
    const response = await axios.get('http://localhost:3000/api/cadres', { params })
    cadres.value = response.data.cadres
    pagination.value.itemCount = response.data.total
  } catch (error) {
    console.error('获取干部列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchUnits = async () => {
  try {
    const response = await axios.get('http://localhost:3000/api/units/options')
    unitOptions.value = response.data.map(unit => ({
      label: unit.name,
      value: unit._id
    }))
  } catch (error) {
    console.error('获取单位列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.value.page = 1
  fetchCadres()
}

const handlePageChange = (page) => {
  pagination.value.page = page
  fetchCadres()
}

const handleEdit = (row) => {
  // TODO: 实现编辑功能
  console.log('编辑干部:', row)
}

const handleDelete = (row) => {
  // TODO: 实现删除功能
  console.log('删除干部:', row)
}

const handleSubmit = () => {
  // TODO: 实现提交功能
  console.log('提交表单:', formData.value)
  showAddModal.value = false
}

onMounted(() => {
  fetchCadres()
  fetchUnits()
})
</script>
