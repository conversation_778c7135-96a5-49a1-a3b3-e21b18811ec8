<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">授课计划</h1>
      <n-button type="primary">
        <template #icon>
          <n-icon :component="AddOutline" />
        </template>
        新增计划
      </n-button>
    </div>

    <n-card>
      <div class="text-center py-20">
        <n-icon :component="CalendarOutline" size="64" class="text-gray-400 mb-4" />
        <p class="text-gray-500">授课计划功能开发中...</p>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { NCard, NButton, NIcon } from 'naive-ui'
import { AddOutline, CalendarOutline } from '@vicons/ionicons5'
</script>
