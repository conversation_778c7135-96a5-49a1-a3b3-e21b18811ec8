# 干部授课管理系统 API 接口文档

## 基本信息

- **基础URL**: `http://localhost:3000/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: RESTful API

## 通用响应格式

### 成功响应
```json
{
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "error": "错误信息",
  "message": "详细错误描述"
}
```

## 1. 系统健康检查

### 1.1 健康检查
- **接口**: `GET /health`
- **描述**: 检查系统运行状态
- **参数**: 无

**响应示例**:
```json
{
  "status": "OK",
  "message": "干部授课管理系统后端服务运行正常",
  "timestamp": "2025-06-08T09:13:25.182Z"
}
```

## 2. 干部管理 (Cadres)

### 2.1 获取干部列表
- **接口**: `GET /cadres`
- **描述**: 分页获取干部列表，支持搜索和筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认10 |
| search | string | 否 | 搜索关键词（姓名、职务、专长） |
| unitId | string | 否 | 单位ID筛选 |
| level | string | 否 | 级别筛选 |
| cadreType | string | 否 | 干部类型筛选 |

**响应示例**:
```json
{
  "cadres": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "name": "李明",
      "gender": "男",
      "birthDate": "1975-03-15T00:00:00.000Z",
      "age": 50,
      "level": "厅局级",
      "position": "司长",
      "cadreType": "厅局级干部",
      "unitId": {
        "_id": "507f1f77bcf86cd799439012",
        "name": "中共中央组织部",
        "shortName": "中组部"
      },
      "proposedTeachingDirections": ["坚持党的全面领导", "全面从严治党"],
      "intendedUniversity1": {
        "_id": "507f1f77bcf86cd799439013",
        "schoolName": "清华大学"
      }
    }
  ],
  "totalPages": 5,
  "currentPage": 1,
  "total": 50
}
```

### 2.2 获取单个干部信息
- **接口**: `GET /cadres/:id`
- **描述**: 根据ID获取干部详细信息

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 干部ID |

### 2.3 创建干部
- **接口**: `POST /cadres`
- **描述**: 创建新的干部记录

**请求体**:
```json
{
  "name": "张三",
  "gender": "男",
  "birthDate": "1980-01-01",
  "nativePlace": "北京市",
  "level": "厅局级",
  "position": "副司长",
  "cadreType": "厅局级干部",
  "unitId": "507f1f77bcf86cd799439012",
  "highestEducation": "博士研究生",
  "highestDegree": "博士",
  "expertise": "组织管理",
  "proposedTeachingDirections": ["坚持党的全面领导"],
  "intendedUniversity1": "507f1f77bcf86cd799439013",
  "remarks": "备注信息"
}
```

### 2.4 更新干部信息
- **接口**: `PUT /cadres/:id`
- **描述**: 更新干部信息

### 2.5 删除干部
- **接口**: `DELETE /cadres/:id`
- **描述**: 删除干部记录

### 2.6 获取拟授课方向选项
- **接口**: `GET /cadres/teaching-directions`
- **描述**: 获取所有可选的授课方向

**响应示例**:
```json
[
  "导论",
  "新时代坚持和发展中国特色社会主义",
  "以中国式现代化全面推进中华民族伟大复兴",
  "坚持党的全面领导",
  "坚持以人民为中心",
  "全面深化改革开放",
  "推动高质量发展",
  "社会主义现代化建设的教育、科技、人才战略",
  "发展全过程人民民主",
  "全面依法治国",
  "建设社会主义文化强国",
  "以保障和改善民生为重点加强社会建设",
  "建设社会主义生态文明",
  "维护和塑造国家安全",
  "建设巩固国防和强大人民军队",
  "坚持\"一国两制\"和推进祖国完全统一",
  "中国特色大国外交和推动构建人类命运共同体",
  "全面从严治党"
]
```

## 3. 单位管理 (Units)

### 3.1 获取单位列表
- **接口**: `GET /units`
- **描述**: 分页获取单位列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认10 |
| search | string | 否 | 搜索关键词 |
| unitType | string | 否 | 单位类型筛选 |
| unitLevel | string | 否 | 单位级别筛选 |

**响应示例**:
```json
{
  "units": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "name": "中共中央组织部",
      "shortName": "中组部",
      "calibrationCode": "ZZB001",
      "receiptCode": "R001",
      "unitType": "国家机关",
      "unitLevel": "正部级",
      "contactPerson": "张三",
      "landlinePhone": "010-12345678",
      "mobilePhone": "13800138001",
      "cadreCount": 5
    }
  ],
  "totalPages": 3,
  "currentPage": 1,
  "total": 25
}
```

### 3.2 获取单位选项
- **接口**: `GET /units/options`
- **描述**: 获取单位选项列表（用于下拉选择）

**响应示例**:
```json
[
  {
    "_id": "507f1f77bcf86cd799439012",
    "name": "中共中央组织部",
    "shortName": "中组部"
  }
]
```

### 3.3 创建单位
- **接口**: `POST /units`
- **描述**: 创建新的单位

**请求体**:
```json
{
  "name": "教育部",
  "shortName": "教育部",
  "calibrationCode": "JYB001",
  "receiptCode": "R002",
  "unitType": "国家机关",
  "unitLevel": "正部级",
  "contactPerson": "李四",
  "landlinePhone": "010-12345679",
  "mobilePhone": "13800138002",
  "remarks": "负责全国教育工作"
}
```

## 4. 高校管理 (Universities)

### 4.1 获取高校列表
- **接口**: `GET /universities`
- **描述**: 分页获取高校列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认10 |
| search | string | 否 | 搜索关键词 |
| location | string | 否 | 地域筛选 |
| isCentral | boolean | 否 | 是否中央直属 |
| governingDepartment | string | 否 | 主管部门筛选 |

**响应示例**:
```json
{
  "universities": [
    {
      "_id": "507f1f77bcf86cd799439013",
      "schoolName": "清华大学",
      "schoolCode": "THU001",
      "governingDepartment": "教育部",
      "location": "北京",
      "isCentral": true,
      "contactDepartment": "继续教育学院",
      "contactPosition": "院长",
      "contactName": "赵六",
      "landlinePhone": "010-62785001",
      "mobilePhone": "13800138004",
      "planCount": 10
    }
  ],
  "totalPages": 4,
  "currentPage": 1,
  "total": 35
}
```

### 4.2 获取高校选项
- **接口**: `GET /universities/options`
- **描述**: 获取高校选项列表（用于下拉选择）

## 5. 授课计划管理 (Plans)

### 5.1 获取授课计划列表
- **接口**: `GET /plans`
- **描述**: 分页获取授课计划列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认10 |
| search | string | 否 | 搜索关键词 |
| teachingStatus | string | 否 | 授课状态筛选 |
| teachingSemester | string | 否 | 授课学期筛选 |
| startDate | string | 否 | 开始日期筛选 |
| endDate | string | 否 | 结束日期筛选 |
| cadreId | string | 否 | 干部ID筛选 |
| universityId | string | 否 | 高校ID筛选 |

**响应示例**:
```json
{
  "plans": [
    {
      "_id": "507f1f77bcf86cd799439014",
      "cadreId": {
        "_id": "507f1f77bcf86cd799439011",
        "name": "李明",
        "position": "司长",
        "level": "厅局级",
        "unitId": {
          "name": "中共中央组织部",
          "shortName": "中组部"
        }
      },
      "universityId": {
        "_id": "507f1f77bcf86cd799439013",
        "schoolName": "清华大学",
        "location": "北京",
        "isCentral": true
      },
      "courseSeries": "党建与廉政",
      "courseName": "新时代党的建设总要求",
      "classDate": "2025-06-15T00:00:00.000Z",
      "classTime": "09:00-11:00",
      "location": "清华大学主楼报告厅",
      "plannedHours": 2,
      "teachingStatus": "计划中",
      "teachingSemester": "2024-2025学年第二学期"
    }
  ],
  "totalPages": 8,
  "currentPage": 1,
  "total": 75
}
```

### 5.2 获取本周授课计划
- **接口**: `GET /plans/this-week`
- **描述**: 获取本周的授课计划

### 5.3 创建授课计划
- **接口**: `POST /plans`
- **描述**: 创建新的授课计划

**请求体**:
```json
{
  "cadreId": "507f1f77bcf86cd799439011",
  "universityId": "507f1f77bcf86cd799439013",
  "courseSeries": "党建与廉政",
  "courseName": "新时代党的建设总要求",
  "classDate": "2025-06-15",
  "classTime": "09:00-11:00",
  "location": "清华大学主楼报告厅",
  "plannedHours": 2,
  "teachingStatus": "计划中",
  "teachingSemester": "2024-2025学年第二学期",
  "remarks": "重点讲解党建理论"
}
```

## 6. 仪表板数据 (Dashboard)

### 6.1 获取统计概览
- **接口**: `GET /dashboard/overview`
- **描述**: 获取系统统计概览数据

**响应示例**:
```json
{
  "totalCadres": 50,
  "totalUnits": 25,
  "totalUniversities": 35,
  "totalPlans": 75,
  "completedPlans": 30,
  "ongoingPlans": 15,
  "plannedPlans": 30,
  "completionRate": "40.0"
}
```

### 6.2 获取月度授课统计
- **接口**: `GET /dashboard/monthly-teaching`
- **描述**: 获取月度授课量对比数据

### 6.3 获取授课状态分布
- **接口**: `GET /dashboard/teaching-status`
- **描述**: 获取授课状态分布统计

### 6.4 获取干部级别分布
- **接口**: `GET /dashboard/cadre-level`
- **描述**: 获取干部级别分布统计

### 6.5 获取热门授课方向
- **接口**: `GET /dashboard/teaching-directions`
- **描述**: 获取热门授课方向统计

### 6.6 获取单位类型分布
- **接口**: `GET /dashboard/unit-type`
- **描述**: 获取单位类型分布统计

### 6.7 获取高校地域分布
- **接口**: `GET /dashboard/university-location`
- **描述**: 获取高校地域分布统计

### 6.8 获取季度完成率趋势
- **接口**: `GET /dashboard/quarterly-completion`
- **描述**: 获取季度完成率趋势数据

## 数据字典

### 干部级别 (Level)
- `省级` - 省级
- `厅局级` - 厅局级
- `县处级` - 县处级
- `乡科级` - 乡科级
- `其他` - 其他

### 干部类型 (CadreType)
- `厅局级干部` - 厅局级干部
- `中管干部` - 中管干部

### 单位类型 (UnitType)
- `国家机关` - 国家机关
- `事业单位` - 事业单位
- `国有企业` - 国有企业
- `社会团体` - 社会团体
- `其他` - 其他

### 单位级别 (UnitLevel)
- `正部级` - 正部级
- `副部级` - 副部级
- `正局级` - 正局级
- `副局级` - 副局级
- `正处级` - 正处级
- `其他` - 其他

### 授课状态 (TeachingStatus)
- `计划中` - 计划中
- `进行中` - 进行中
- `已完成` - 已完成
- `已取消` - 已取消
- `已调整` - 已调整

### 课程系列 (CourseSeries)
- `领导力与战略管理` - 领导力与战略管理
- `公共政策与治理` - 公共政策与治理
- `经济发展与改革` - 经济发展与改革
- `社会建设与民生` - 社会建设与民生
- `文化建设与传承` - 文化建设与传承
- `生态文明建设` - 生态文明建设
- `国防与安全` - 国防与安全
- `外交与国际关系` - 外交与国际关系
- `党建与廉政` - 党建与廉政
- `其他` - 其他

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有日期时间格式均为 ISO 8601 标准格式
2. 分页参数 `page` 从 1 开始
3. 所有 ID 字段均为 MongoDB ObjectId 格式
4. 创建和更新操作会自动验证数据完整性
5. 删除操作会检查关联数据，防止数据不一致
