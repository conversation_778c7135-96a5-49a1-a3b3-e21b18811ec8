const Unit = require('../models/Unit');

// 获取所有单位
const getAllUnits = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, unitType, unitLevel } = req.query;
    
    // 构建查询条件
    const query = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { shortName: { $regex: search, $options: 'i' } },
        { contactPerson: { $regex: search, $options: 'i' } }
      ];
    }
    if (unitType) query.unitType = unitType;
    if (unitLevel) query.unitLevel = unitLevel;

    const units = await Unit.find(query)
      .populate('cadreCount')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Unit.countDocuments(query);

    res.json({
      units,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: '获取单位列表失败', message: error.message });
  }
};

// 根据ID获取单位
const getUnitById = async (req, res) => {
  try {
    const unit = await Unit.findById(req.params.id).populate('cadreCount');
    
    if (!unit) {
      return res.status(404).json({ error: '单位不存在' });
    }
    
    res.json(unit);
  } catch (error) {
    res.status(500).json({ error: '获取单位信息失败', message: error.message });
  }
};

// 创建单位
const createUnit = async (req, res) => {
  try {
    const unit = new Unit(req.body);
    await unit.save();
    
    res.status(201).json(unit);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: '数据验证失败', message: error.message });
    }
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ error: `${field === 'name' ? '单位名称' : '校准编号'}已存在` });
    }
    res.status(500).json({ error: '创建单位失败', message: error.message });
  }
};

// 更新单位
const updateUnit = async (req, res) => {
  try {
    const unit = await Unit.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!unit) {
      return res.status(404).json({ error: '单位不存在' });
    }
    
    res.json(unit);
  } catch (error) {
    if (error.name === 'ValidationError') {
      return res.status(400).json({ error: '数据验证失败', message: error.message });
    }
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({ error: `${field === 'name' ? '单位名称' : '校准编号'}已存在` });
    }
    res.status(500).json({ error: '更新单位失败', message: error.message });
  }
};

// 删除单位
const deleteUnit = async (req, res) => {
  try {
    // 检查是否有关联的干部
    const Cadre = require('../models/Cadre');
    const cadreCount = await Cadre.countDocuments({ unitId: req.params.id });
    
    if (cadreCount > 0) {
      return res.status(400).json({ error: '该单位下还有干部，无法删除' });
    }

    const unit = await Unit.findByIdAndDelete(req.params.id);
    
    if (!unit) {
      return res.status(404).json({ error: '单位不存在' });
    }
    
    res.json({ message: '单位删除成功' });
  } catch (error) {
    res.status(500).json({ error: '删除单位失败', message: error.message });
  }
};

// 获取单位选项（用于下拉选择）
const getUnitOptions = async (req, res) => {
  try {
    const units = await Unit.find({}, 'name shortName').sort({ name: 1 });
    res.json(units);
  } catch (error) {
    res.status(500).json({ error: '获取单位选项失败', message: error.message });
  }
};

module.exports = {
  getAllUnits,
  getUnitById,
  createUnit,
  updateUnit,
  deleteUnit,
  getUnitOptions
};
