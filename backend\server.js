const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 数据库连接
let isMongoConnected = false;

// MongoDB Atlas 连接配置
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.DATABASE_URL, {
      // MongoDB Atlas 连接选项
      serverSelectionTimeoutMS: 5000, // 5秒超时
      socketTimeoutMS: 45000, // 45秒socket超时
    });

    console.log(`MongoDB Atlas 连接成功: ${conn.connection.host}`);
    console.log(`数据库名称: ${conn.connection.name}`);
    isMongoConnected = true;

    // 测试连接
    await mongoose.connection.db.admin().ping();
    console.log("数据库连接测试成功!");

  } catch (error) {
    console.error('MongoDB Atlas 连接失败:', error.message);
    console.log('系统将在无数据库模式下运行，使用模拟数据');
    isMongoConnected = false;
  }
};

// 连接数据库
connectDB();

// 导出连接状态
app.locals.isMongoConnected = () => isMongoConnected;

// 路由
const cadreRoutes = require('./src/routes/cadreRoutes');
const unitRoutes = require('./src/routes/unitRoutes');
const universityRoutes = require('./src/routes/universityRoutes');
const planRoutes = require('./src/routes/planRoutes');
const dashboardRoutes = require('./src/routes/dashboardRoutes');

app.use('/api/cadres', cadreRoutes);
app.use('/api/units', unitRoutes);
app.use('/api/universities', universityRoutes);
app.use('/api/plans', planRoutes);
app.use('/api/dashboard', dashboardRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: '干部授课管理系统后端服务运行正常',
    timestamp: new Date().toISOString()
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? err.message : '请联系管理员'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/api/health`);
});
