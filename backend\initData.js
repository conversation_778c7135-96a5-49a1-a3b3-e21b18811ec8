const mongoose = require('mongoose');
const seedData = require('./src/utils/seedData');
require('dotenv').config();

const initializeData = async () => {
  try {
    // 连接数据库
    await mongoose.connect(process.env.DATABASE_URL);
    console.log('MongoDB 连接成功');

    // 检查是否使用 force 参数
    const force = process.argv.includes('--force');

    // 初始化数据
    await seedData(force);

    // 关闭连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');

  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  }
};

console.log('使用方法:');
console.log('  node initData.js        - 检查并初始化数据（如果数据库为空）');
console.log('  node initData.js --force - 强制重新初始化数据（清空现有数据）');
console.log('');

initializeData();
