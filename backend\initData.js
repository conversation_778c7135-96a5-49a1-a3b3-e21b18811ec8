const mongoose = require('mongoose');
const seedData = require('./src/utils/seedData');
require('dotenv').config();

const initializeData = async () => {
  try {
    // 连接数据库
    await mongoose.connect(process.env.DATABASE_URL);
    console.log('MongoDB 连接成功');
    
    // 初始化数据
    await seedData();
    
    // 关闭连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
    
  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  }
};

initializeData();
