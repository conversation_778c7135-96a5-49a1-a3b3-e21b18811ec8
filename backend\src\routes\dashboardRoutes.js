const express = require('express');
const router = express.Router();
const {
  getOverviewStats,
  getMonthlyTeachingStats,
  getTeachingStatusDistribution,
  getCadreLevelDistribution,
  getPopularTeachingDirections,
  getUnitTypeDistribution,
  getUniversityLocationDistribution,
  getQuarterlyCompletionTrend
} = require('../controllers/dashboardController');

// GET /api/dashboard/overview - 获取统计概览数据
router.get('/overview', getOverviewStats);

// GET /api/dashboard/monthly-teaching - 获取月度授课量对比数据
router.get('/monthly-teaching', getMonthlyTeachingStats);

// GET /api/dashboard/teaching-status - 获取授课状态分布
router.get('/teaching-status', getTeachingStatusDistribution);

// GET /api/dashboard/cadre-level - 获取干部级别分布
router.get('/cadre-level', getCadreLevelDistribution);

// GET /api/dashboard/teaching-directions - 获取热门授课方向统计
router.get('/teaching-directions', getPopularTeachingDirections);

// GET /api/dashboard/unit-type - 获取单位类型分布
router.get('/unit-type', getUnitTypeDistribution);

// GET /api/dashboard/university-location - 获取高校地域分布
router.get('/university-location', getUniversityLocationDistribution);

// GET /api/dashboard/quarterly-completion - 获取季度完成率趋势
router.get('/quarterly-completion', getQuarterlyCompletionTrend);

module.exports = router;
