const express = require('express');
const router = express.Router();
const {
  getAllCadres,
  getCadreById,
  createCadre,
  updateCadre,
  deleteCadre,
  getTeachingDirections
} = require('../controllers/cadreController');

// GET /api/cadres - 获取所有干部
router.get('/', getAllCadres);

// GET /api/cadres/teaching-directions - 获取拟授课方向选项
router.get('/teaching-directions', getTeachingDirections);

// GET /api/cadres/:id - 根据ID获取干部
router.get('/:id', getCadreById);

// POST /api/cadres - 创建干部
router.post('/', createCadre);

// PUT /api/cadres/:id - 更新干部
router.put('/:id', updateCadre);

// DELETE /api/cadres/:id - 删除干部
router.delete('/:id', deleteCadre);

module.exports = router;
