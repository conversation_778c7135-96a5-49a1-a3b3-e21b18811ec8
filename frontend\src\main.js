import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import App from './App.vue'
import './style.css'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('./views/Dashboard.vue')
  },
  {
    path: '/cadres',
    name: 'Cadres',
    component: () => import('./views/Cadres.vue')
  },
  {
    path: '/units',
    name: 'Units',
    component: () => import('./views/Units.vue')
  },
  {
    path: '/universities',
    name: 'Universities',
    component: () => import('./views/Universities.vue')
  },
  {
    path: '/plans',
    name: 'Plans',
    component: () => import('./views/Plans.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()
const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(naive)
app.mount('#app')
