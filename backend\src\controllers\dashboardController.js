const Cadre = require('../models/Cadre');
const Unit = require('../models/Unit');
const University = require('../models/University');
const Plan = require('../models/Plan');

// 获取统计概览数据
const getOverviewStats = async (req, res) => {
  try {
    // 检查MongoDB连接状态
    if (req.app.locals.isMongoConnected && req.app.locals.isMongoConnected()) {
      const [
        totalCadres,
        totalUnits,
        totalUniversities,
        totalPlans,
        completedPlans,
        ongoingPlans,
        plannedPlans
      ] = await Promise.all([
        Cadre.countDocuments(),
        Unit.countDocuments(),
        University.countDocuments(),
        Plan.countDocuments(),
        Plan.countDocuments({ teachingStatus: '已完成' }),
        Plan.countDocuments({ teachingStatus: '进行中' }),
        Plan.countDocuments({ teachingStatus: '计划中' })
      ]);

      res.json({
        totalCadres,
        totalUnits,
        totalUniversities,
        totalPlans,
        completedPlans,
        ongoingPlans,
        plannedPlans,
        completionRate: totalPlans > 0 ? ((completedPlans / totalPlans) * 100).toFixed(1) : 0
      });
    } else {
      // 返回模拟数据
      const mockData = {
        totalCadres: 25,
        totalUnits: 12,
        totalUniversities: 18,
        totalPlans: 45,
        completedPlans: 20,
        ongoingPlans: 8,
        plannedPlans: 17,
        completionRate: "44.4"
      };
      res.json(mockData);
    }
  } catch (error) {
    res.status(500).json({ error: '获取统计数据失败', message: error.message });
  }
};

// 获取月度授课量对比数据
const getMonthlyTeachingStats = async (req, res) => {
  try {
    if (req.app.locals.isMongoConnected && req.app.locals.isMongoConnected()) {
      const currentYear = new Date().getFullYear();
      const monthlyStats = [];

      for (let month = 1; month <= 12; month++) {
        const startDate = new Date(currentYear, month - 1, 1);
        const endDate = new Date(currentYear, month, 0);

        const [planned, completed] = await Promise.all([
          Plan.countDocuments({
            classDate: { $gte: startDate, $lte: endDate }
          }),
          Plan.countDocuments({
            classDate: { $gte: startDate, $lte: endDate },
            teachingStatus: '已完成'
          })
        ]);

        monthlyStats.push({
          month: `${month}月`,
          planned,
          completed
        });
      }

      res.json(monthlyStats);
    } else {
      // 返回模拟数据
      const mockData = [
        { month: '1月', planned: 5, completed: 4 },
        { month: '2月', planned: 3, completed: 2 },
        { month: '3月', planned: 8, completed: 6 },
        { month: '4月', planned: 6, completed: 5 },
        { month: '5月', planned: 7, completed: 6 },
        { month: '6月', planned: 9, completed: 7 },
        { month: '7月', planned: 4, completed: 3 },
        { month: '8月', planned: 2, completed: 2 },
        { month: '9月', planned: 6, completed: 4 },
        { month: '10月', planned: 8, completed: 6 },
        { month: '11月', planned: 5, completed: 4 },
        { month: '12月', planned: 3, completed: 2 }
      ];
      res.json(mockData);
    }
  } catch (error) {
    res.status(500).json({ error: '获取月度统计数据失败', message: error.message });
  }
};

// 获取授课状态分布
const getTeachingStatusDistribution = async (req, res) => {
  try {
    if (req.app.locals.isMongoConnected && req.app.locals.isMongoConnected()) {
      const statusStats = await Plan.aggregate([
        {
          $group: {
            _id: '$teachingStatus',
            count: { $sum: 1 }
          }
        }
      ]);

      const distribution = statusStats.map(stat => ({
        status: stat._id,
        count: stat.count
      }));

      res.json(distribution);
    } else {
      // 返回模拟数据
      const mockData = [
        { status: '计划中', count: 17 },
        { status: '进行中', count: 8 },
        { status: '已完成', count: 20 }
      ];
      res.json(mockData);
    }
  } catch (error) {
    res.status(500).json({ error: '获取状态分布数据失败', message: error.message });
  }
};

// 获取干部级别分布
const getCadreLevelDistribution = async (req, res) => {
  try {
    const levelStats = await Cadre.aggregate([
      {
        $group: {
          _id: '$level',
          count: { $sum: 1 }
        }
      }
    ]);

    const distribution = levelStats.map(stat => ({
      level: stat._id || '未设置',
      count: stat.count
    }));

    res.json(distribution);
  } catch (error) {
    res.status(500).json({ error: '获取干部级别分布数据失败', message: error.message });
  }
};

// 获取热门授课方向统计
const getPopularTeachingDirections = async (req, res) => {
  try {
    const directionStats = await Cadre.aggregate([
      { $unwind: '$proposedTeachingDirections' },
      {
        $group: {
          _id: '$proposedTeachingDirections',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    const directions = directionStats.map(stat => ({
      direction: stat._id,
      count: stat.count
    }));

    res.json(directions);
  } catch (error) {
    res.status(500).json({ error: '获取授课方向统计数据失败', message: error.message });
  }
};

// 获取单位类型分布
const getUnitTypeDistribution = async (req, res) => {
  try {
    const typeStats = await Unit.aggregate([
      {
        $group: {
          _id: '$unitType',
          count: { $sum: 1 }
        }
      }
    ]);

    const distribution = typeStats.map(stat => ({
      type: stat._id,
      count: stat.count
    }));

    res.json(distribution);
  } catch (error) {
    res.status(500).json({ error: '获取单位类型分布数据失败', message: error.message });
  }
};

// 获取高校地域分布
const getUniversityLocationDistribution = async (req, res) => {
  try {
    const locationStats = await University.aggregate([
      {
        $group: {
          _id: '$location',
          count: { $sum: 1 }
        }
      }
    ]);

    const distribution = locationStats.map(stat => ({
      location: stat._id || '其他',
      count: stat.count
    }));

    res.json(distribution);
  } catch (error) {
    res.status(500).json({ error: '获取高校地域分布数据失败', message: error.message });
  }
};

// 获取季度完成率趋势
const getQuarterlyCompletionTrend = async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();
    const quarterlyStats = [];

    for (let quarter = 1; quarter <= 4; quarter++) {
      const startMonth = (quarter - 1) * 3;
      const startDate = new Date(currentYear, startMonth, 1);
      const endDate = new Date(currentYear, startMonth + 3, 0);

      const [totalPlans, completedPlans] = await Promise.all([
        Plan.countDocuments({
          classDate: { $gte: startDate, $lte: endDate }
        }),
        Plan.countDocuments({
          classDate: { $gte: startDate, $lte: endDate },
          teachingStatus: '已完成'
        })
      ]);

      const completionRate = totalPlans > 0 ? ((completedPlans / totalPlans) * 100).toFixed(1) : 0;

      quarterlyStats.push({
        quarter: `Q${quarter}`,
        totalPlans,
        completedPlans,
        completionRate: parseFloat(completionRate)
      });
    }

    res.json(quarterlyStats);
  } catch (error) {
    res.status(500).json({ error: '获取季度趋势数据失败', message: error.message });
  }
};

module.exports = {
  getOverviewStats,
  getMonthlyTeachingStats,
  getTeachingStatusDistribution,
  getCadreLevelDistribution,
  getPopularTeachingDirections,
  getUnitTypeDistribution,
  getUniversityLocationDistribution,
  getQuarterlyCompletionTrend
};
