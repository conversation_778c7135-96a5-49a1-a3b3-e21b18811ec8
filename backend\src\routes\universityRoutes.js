const express = require('express');
const router = express.Router();
const {
  getAllUniversities,
  getUniversityById,
  createUniversity,
  updateUniversity,
  deleteUniversity,
  getUniversityOptions
} = require('../controllers/universityController');

// GET /api/universities - 获取所有高校
router.get('/', getAllUniversities);

// GET /api/universities/options - 获取高校选项（用于下拉选择）
router.get('/options', getUniversityOptions);

// GET /api/universities/:id - 根据ID获取高校
router.get('/:id', getUniversityById);

// POST /api/universities - 创建高校
router.post('/', createUniversity);

// PUT /api/universities/:id - 更新高校
router.put('/:id', updateUniversity);

// DELETE /api/universities/:id - 删除高校
router.delete('/:id', deleteUniversity);

module.exports = router;
