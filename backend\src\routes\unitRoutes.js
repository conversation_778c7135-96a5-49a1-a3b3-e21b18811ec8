const express = require('express');
const router = express.Router();
const {
  getAllUnits,
  getUnitById,
  createUnit,
  updateUnit,
  deleteUnit,
  getUnitOptions
} = require('../controllers/unitController');

// GET /api/units - 获取所有单位
router.get('/', getAllUnits);

// GET /api/units/options - 获取单位选项（用于下拉选择）
router.get('/options', getUnitOptions);

// GET /api/units/:id - 根据ID获取单位
router.get('/:id', getUnitById);

// POST /api/units - 创建单位
router.post('/', createUnit);

// PUT /api/units/:id - 更新单位
router.put('/:id', updateUnit);

// DELETE /api/units/:id - 删除单位
router.delete('/:id', deleteUnit);

module.exports = router;
