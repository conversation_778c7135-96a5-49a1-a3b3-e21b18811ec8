/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  width: 100%;
}

body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
  overflow-x: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 应用容器 */
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: none !important;
}

/* 头部样式 */
.header {
  width: 100%;
  height: 64px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.header-content {
  width: 100%;
  height: 100%;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 主布局 */
.main-layout {
  display: flex;
  width: 100%;
  height: calc(100vh - 64px);
  flex: 1;
}

/* 侧边栏 */
.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e5e7eb;
  height: 100%;
  flex-shrink: 0;
}

/* 主内容区 */
.main-content {
  flex: 1;
  background: #f8fafc;
  overflow-y: auto;
  height: 100%;
  width: calc(100vw - 240px);
}

.content-wrapper {
  width: 100%;
  max-width: none;
  padding: 24px;
  min-height: 100%;
}

/* 工具类 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 24px;
  width: 100%;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:hover {
  background: #f9fafb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
}

.input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 100%;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.border-b {
  border-bottom: 1px solid #e5e7eb;
}

.ml-auto {
  margin-left: auto;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.flex-1 {
  flex: 1;
}

.grid {
  display: grid;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.text-center {
  text-align: center;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

/* 状态标签样式 */
.bg-blue-100 { background-color: #dbeafe; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.bg-green-100 { background-color: #dcfce7; }
.text-green-600 { color: #16a34a; }
.text-green-800 { color: #166534; }
.bg-purple-100 { background-color: #f3e8ff; }
.text-purple-600 { color: #9333ea; }
.text-purple-800 { color: #6b21a8; }
.bg-orange-100 { background-color: #fed7aa; }
.text-orange-600 { color: #ea580c; }
.text-orange-800 { color: #c2410c; }
.bg-yellow-100 { background-color: #fef3c7; }
.text-yellow-800 { color: #92400e; }
.bg-red-100 { background-color: #fee2e2; }
.text-red-600 { color: #dc2626; }
.text-red-800 { color: #991b1b; }
.bg-gray-100 { background-color: #f3f4f6; }
.text-gray-800 { color: #1f2937; }

/* 圆角样式 */
.rounded-full { border-radius: 9999px; }
.rounded-lg { border-radius: 8px; }

/* 宽度高度 */
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }

/* 表格样式增强 */
table {
  border-collapse: collapse;
}

table th {
  background-color: #f9fafb;
}

table tr:hover {
  background-color: #f9fafb;
}

/* 按钮悬停效果 */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片悬停效果 */
.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 页面专用样式 */
.dashboard-container {
  width: 100%;
  height: 100%;
  max-width: none;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  width: 100%;
}

/* 强制全宽布局 */
* {
  max-width: none !important;
}

.w-full, .grid, .card, .main-content, .content-wrapper,
.dashboard-container, .page-header, table, .overflow-x-auto {
  max-width: none !important;
  width: 100% !important;
}

/* 网格布局全宽 */
.grid {
  width: 100% !important;
  max-width: none !important;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}
